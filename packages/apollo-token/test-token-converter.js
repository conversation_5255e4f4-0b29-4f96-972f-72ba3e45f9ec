/**
 * Simple test to verify the tokenToCss utility works
 */

const { 
  convertTokensToCSS, 
  convertColorTokensToCSS, 
  getCSSVariableReference 
} = require('./dist/index.umd.js');

console.log('🧪 Testing Apollo Token to CSS Converter\n');

try {
  // Test 1: Basic CSS conversion
  console.log('Test 1: Basic CSS conversion');
  const basicResult = convertTokensToCSS({
    useCustomProperties: true,
    prefix: 'apl',
    includeBase: true,
    includeAlias: false,
    categories: ['color']
  });
  
  console.log('✅ Generated', basicResult.properties.length, 'CSS properties');
  console.log('Sample CSS preview:', basicResult.css.substring(0, 200) + '...\n');

  // Test 2: Color tokens only
  console.log('Test 2: Color tokens conversion');
  const colorResult = convertColorTokensToCSS({
    prefix: 'apl-color'
  });
  
  console.log('✅ Generated', colorResult.properties.length, 'color properties');
  console.log('Variable map size:', colorResult.variableMap.size, '\n');

  // Test 3: CSS variable references
  console.log('Test 3: CSS variable references');
  const colorRef = getCSSVariableReference('base.color.green-pine.50', 'apl');
  console.log('✅ Color reference:', colorRef, '\n');

  // Test 4: Regular CSS properties (no custom properties)
  console.log('Test 4: Regular CSS properties');
  const regularResult = convertTokensToCSS({
    useCustomProperties: false,
    includeBase: true,
    includeAlias: false,
    categories: ['spacing']
  });
  
  console.log('✅ Generated regular CSS properties');
  console.log('Sample properties:', regularResult.properties.slice(0, 3).join('; '), '\n');

  console.log('🎉 All tests completed successfully!');
  
  // Show a sample of the generated CSS
  console.log('\n📄 Sample Generated CSS:');
  console.log('='.repeat(50));
  console.log(basicResult.css.substring(0, 500) + '...');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
