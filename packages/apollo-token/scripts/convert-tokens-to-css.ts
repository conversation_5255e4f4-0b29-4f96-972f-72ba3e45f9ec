#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const TOKENS_FILE = path.join(__dirname, '../src/tokens/ts/apollo.ts');
const OUTPUT_FILE = path.join(__dirname, '../build/apollo-tokens.css');

/**
 * Convert camelCase or kebab-case to CSS custom property format
 */
function toCSSVariableName(key: string, prefix: string = 'apollo'): string {
  // Convert camelCase to kebab-case
  const kebabCase = key.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
  return `--${prefix}-${kebabCase}`;
}

/**
 * Recursively convert token object to CSS custom properties
 */
function tokensToCSSProperties(
  obj: any, 
  prefix: string = 'apollo', 
  currentPath: string[] = []
): string[] {
  const cssProperties: string[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const newPath = [...currentPath, key];
    const variableName = toCSSVariableName(newPath.join('-'), prefix);

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      // Recursively process nested objects
      cssProperties.push(...tokensToCSSProperties(value, prefix, newPath));
    } else {
      // Convert value to CSS-compatible format
      let cssValue = String(value);
      
      // Handle special cases
      if (typeof value === 'number') {
        // For font weights, keep as numbers
        if (newPath.some(part => part.includes('weight'))) {
          cssValue = String(value);
        } else {
          // For other numbers, assume they need px units if they don't have units
          cssValue = cssValue.includes('px') || cssValue.includes('%') || cssValue.includes('rem') || cssValue.includes('em') 
            ? cssValue 
            : `${value}px`;
        }
      }
      
      // Handle token references (e.g., "{color.green-pine.40}")
      if (cssValue.startsWith('{') && cssValue.endsWith('}')) {
        const tokenPath = cssValue.slice(1, -1);
        const referencedVariableName = toCSSVariableName(tokenPath.replace(/\./g, '-'), prefix);
        cssValue = `var(${referencedVariableName})`;
      }

      cssProperties.push(`  ${variableName}: ${cssValue};`);
    }
  }

  return cssProperties;
}

/**
 * Generate CSS content with custom properties
 */
function generateCSSContent(tokens: any): string {
  const timestamp = new Date().toISOString();
  
  let cssContent = `/**
 * Apollo Design Tokens - CSS Custom Properties
 * Generated on: ${timestamp}
 * 
 * This file contains all design tokens converted to CSS custom properties
 * from the TypeScript tokens file.
 */

:root {
`;

  // Convert tokens to CSS custom properties
  const cssProperties = tokensToCSSProperties(tokens);
  cssContent += cssProperties.join('\n');

  cssContent += `
}

/* Utility classes for common token usage */
.apollo-font-family {
  font-family: var(--apollo-typography-font-family-ibm-plex-sans-thai);
}

/* Color utility classes */
.apollo-text-primary {
  color: var(--apollo-color-green-pine-40);
}

.apollo-bg-primary {
  background-color: var(--apollo-color-green-pine-40);
}

.apollo-text-secondary {
  color: var(--apollo-color-gray-bluish-40);
}

.apollo-bg-secondary {
  background-color: var(--apollo-color-gray-bluish-40);
}

/* Spacing utility classes */
.apollo-spacing-sm {
  padding: var(--apollo-spacing-16);
}

.apollo-spacing-md {
  padding: var(--apollo-spacing-24);
}

.apollo-spacing-lg {
  padding: var(--apollo-spacing-32);
}

/* Border radius utility classes */
.apollo-radius-sm {
  border-radius: var(--apollo-radius-4);
}

.apollo-radius-md {
  border-radius: var(--apollo-radius-8);
}

.apollo-radius-lg {
  border-radius: var(--apollo-radius-16);
}
`;

  return cssContent;
}

/**
 * Load tokens from TypeScript file
 */
function loadTokens() {
  try {
    // Clear require cache to ensure fresh load
    delete require.cache[require.resolve(TOKENS_FILE)];
    const tokensModule = require(TOKENS_FILE);
    return tokensModule.tokens || tokensModule.default;
  } catch (error) {
    console.error('❌ Error loading tokens file:', error);
    throw error;
  }
}

/**
 * Main function
 */
function main() {
  console.log('🚀 Starting CSS conversion process...\n');
  
  try {
    // Load tokens from TypeScript file
    console.log('📖 Loading tokens from TypeScript file...');
    const tokens = loadTokens();
    
    if (!tokens) {
      console.error('❌ No tokens found in the TypeScript file');
      process.exit(1);
    }
    
    console.log('✅ Tokens loaded successfully');
    console.log(`📊 Found ${Object.keys(tokens).length} top-level token categories`);
    
    // Generate CSS content
    console.log('\n🔄 Converting tokens to CSS custom properties...');
    const cssContent = generateCSSContent(tokens);
    
    // Ensure output directory exists
    const outputDir = path.dirname(OUTPUT_FILE);
    fs.mkdirSync(outputDir, { recursive: true });
    
    // Write CSS file
    fs.writeFileSync(OUTPUT_FILE, cssContent, 'utf8');
    
    console.log(`✅ Successfully generated: ${OUTPUT_FILE}`);
    
    // Show file size
    const stats = fs.statSync(OUTPUT_FILE);
    const fileSizeKB = (stats.size / 1024).toFixed(2);
    console.log(`📊 File size: ${fileSizeKB} KB`);
    
    console.log('\n🎉 CSS conversion completed successfully!');
    console.log('\n📋 Generated content includes:');
    console.log('   - CSS custom properties for all tokens');
    console.log('   - Utility classes for common usage patterns');
    console.log('   - Proper token reference resolution');
    
  } catch (error) {
    console.error('❌ Error during conversion:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  tokensToCSSProperties,
  generateCSSContent,
  toCSSVariableName,
  loadTokens
};
